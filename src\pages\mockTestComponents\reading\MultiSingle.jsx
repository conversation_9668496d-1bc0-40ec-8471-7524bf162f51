import { useEffect, useState } from "react";
import {
  Typography,
  Box,
  Radio,
  RadioGroup,
  FormControlLabel,
  Paper,
  Divider,
  FormControl,
} from "@mui/material";
import PropTypes from "prop-types";

const MultipleChoiceSingle = ({ setAnswer, onNext, setGoNext, question }) => {
  const questionData = question;
  const [selectedOption, setSelectedOption] = useState("");

  // Reset selected option when question changes
  useEffect(() => {
    setSelectedOption("");
  }, [question?.questionId]);

  useEffect(() => {
    setGoNext();
  }, [setGoNext]);

  // Handle radio button change
  const handleChange = (event) => {
    const selectedValue = event.target.value;
    setSelectedOption(selectedValue);

    // Prepare answer data
    const answerData = {
      mockTestId: questionData?.mocktestId,
      questionId: questionData?.questionId,
      section: questionData?.category?.section,
      prompt: questionData?.prompt,
      userId: localStorage.getItem("userId"),
      useranswers: [selectedValue],
      correctAnswer: questionData?.options,
      maxScoreIfCorrect: questionData?.maxScore,
      type: questionData?.category?.name,
    };

    // Store the answer in parent component
    setAnswer(answerData);
  };

  return (
    <Box
      sx={{
        maxWidth: "800px",
        mx: "auto",
        p: { xs: 2, sm: 3 },
        minHeight: "70vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* Task header */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
          mb: 3,
          pb: 2,
          borderBottom: "1px solid rgba(0,0,0,0.08)",
        }}
      >
        <Typography
          variant="h5"
          sx={{
            mb: 1,
            fontWeight: 600,
            color: "text.primary",
          }}
        >
          Multiple Choice
        </Typography>

        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ fontWeight: 500 }}
        >
          {questionData?.category?.description || "Select the best answer"}
        </Typography>
      </Box>

      {/* Question content */}
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, sm: 3 },
          mb: { xs: 3, sm: 4 },
          borderRadius: 2,
          backgroundColor: "#f8f9fa",
          border: "1px solid #e6e8eb",
        }}
      >
        <Typography
          variant="body1"
          sx={{
            fontWeight: 500,
            lineHeight: 1.6,
            mb: 2,
          }}
        >
          {questionData.prompt}
        </Typography>
      </Paper>

      {/* Answer options */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 2, sm: 3 },
          borderRadius: 2,
          backgroundColor: "white",
          flex: 1,
        }}
      >
        <Typography
          variant="subtitle1"
          color="text.secondary"
          sx={{ mb: 2, fontWeight: 500 }}
        >
          Select one option:
        </Typography>

        <Divider sx={{ mb: 2 }} />

        <FormControl component="fieldset" sx={{ width: "100%" }}>
          <RadioGroup
            value={selectedOption}
            onChange={handleChange}
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 1,
            }}
          >
            {questionData?.options?.map((option, index) => (
              <Paper
                key={index}
                elevation={0}
                sx={{
                  border: "1px solid",
                  borderColor:
                    selectedOption === option.text ? "primary.main" : "#e0e0e0",
                  borderRadius: 1.5,
                  transition: "all 0.2s ease",
                  backgroundColor:
                    selectedOption === option.text
                      ? "rgba(25, 118, 210, 0.04)"
                      : "white",
                  "&:hover": {
                    backgroundColor:
                      selectedOption === option.text
                        ? "rgba(25, 118, 210, 0.08)"
                        : "rgba(0, 0, 0, 0.01)",
                    borderColor:
                      selectedOption === option.text
                        ? "primary.main"
                        : "#bdbdbd",
                  },
                }}
              >
                <FormControlLabel
                  value={option.text}
                  control={
                    <Radio
                      color="primary"
                      sx={{
                        "& .MuiSvgIcon-root": {
                          fontSize: 20,
                        },
                      }}
                    />
                  }
                  label={
                    <Typography variant="body1" sx={{ py: 0.5 }}>
                      {option.text}
                    </Typography>
                  }
                  sx={{
                    m: 0,
                    p: 1,
                    width: "100%",
                    "& .MuiFormControlLabel-label": {
                      width: "100%",
                      fontWeight: selectedOption === option.text ? 500 : 400,
                    },
                  }}
                />
              </Paper>
            ))}
          </RadioGroup>
        </FormControl>
      </Paper>
    </Box>
  );
};

MultipleChoiceSingle.propTypes = {
  setAnswer: PropTypes.func.isRequired,
  onNext: PropTypes.func,
  setGoNext: PropTypes.func.isRequired,
  question: PropTypes.shape({
    questionId: PropTypes.string,
    mocktestId: PropTypes.string,
    prompt: PropTypes.string,
    maxScore: PropTypes.number,
    options: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string,
        correct: PropTypes.bool,
      })
    ),
    category: PropTypes.shape({
      name: PropTypes.string,
      description: PropTypes.string,
      section: PropTypes.string,
    }),
  }).isRequired,
};

export default MultipleChoiceSingle;
