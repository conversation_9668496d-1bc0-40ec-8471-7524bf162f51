import { useState, useEffect, useCallback } from "react";
import { Box, Button, Typography, Grid, Modal, Toolbar } from "@mui/material";
import ProgressBar from "./ProgressBar";
import Timer from "./Timer";
import { ComponentData } from "../mockcomponentData";
import { server } from "@/api/services/server";
import { getRequest, postRequest } from "@/api/services/controller";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { useAuth } from "@/components/others/AuthContext";
import { CompleteIntroFlow } from "@/components/mocktest-intro";
import mocktestService from "@/api/services/mocktestService";

function MockTest() {
  const [questions, setQuestions] = useState([]);
  const [totalQuestions, settotalQuestions] = useState([]);
  const params = useParams();
  const navigate = useNavigate();
  const [mockTest, setmockTest] = useState({});
  const { practiceId } = params;
  const { user } = useAuth();

  // Add state to track introduction completion
  const [showIntroduction, setShowIntroduction] = useState(true);
  const [introductionCompleted, setIntroductionCompleted] = useState(false);

  // Add logging-related state
  const [currentLogId, setCurrentLogId] = useState(null);
  const [attemptNumber, setAttemptNumber] = useState(1);
  const [attemptType, setAttemptType] = useState("first_attempt");
  const [testStartTime, setTestStartTime] = useState(null);
  const [sectionTimes, setSectionTimes] = useState({
    speaking: { startTime: null, timeSpent: 0 },
    reading: { startTime: null, timeSpent: 0 },
    listening: { startTime: null, timeSpent: 0 },
  });
  const [isInitializing, setIsInitializing] = useState(true);
  const [listeningTimerStarted, setListeningTimerStarted] = useState(false);

  const dataForParams = ["Speaking & Writing", "Reading", "Listening"];

  // Function to check if a question is "Summarize Spoken Text"
  const isSummarizeSpokenText = (question) => {
    return (
      question?.categoryId === "68556cb5d0001a608d091719" || // Main categoryId
      question?.categoryId === "6849c969d0001a608d091536" || // Alternative categoryId from mocktest_script
      question?.category?.name === "Summarize Spoken Text" ||
      question?.category?.name?.toLowerCase().includes("summarize") ||
      question?.type?.toLowerCase().includes("summarize")
    );
  };

  // Function to check if all Summarize Spoken Text questions are completed
  const areAllSummarizeQuestionsCompleted = () => {
    if (section !== 2 || !questions?.length) return false;

    const summarizeQuestions = questions.filter((q) =>
      isSummarizeSpokenText(q)
    );
    if (summarizeQuestions.length === 0) return true; // No summarize questions

    // Check if current question index is past all summarize questions
    const lastSummarizeIndex = questions.findLastIndex((q) =>
      isSummarizeSpokenText(q)
    );
    return currentQuestion > lastSummarizeIndex;
  };

  const [section, setsection] = useState(2);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [showPopup, setshowPopup] = useState(false);
  const [testStarted, setTestStarted] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0); // 10 minutes timer
  const [canGoNext, setcanGoNext] = useState(true);
  const [showNextModal, setshowNextModal] = useState(false);
  const [answers] = useState([]);
  const [currentAnswer, setcurrentAnswer] = useState({});
  const [warningMessage, setwarningMessage] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [showTestCompleteModal, setShowTestCompleteModal] = useState(false);
  // Track whether the current question has been submitted
  const [answerSubmitted, setAnswerSubmitted] = useState(false);

  // Handle introduction completion
  const handleIntroductionComplete = () => {
    setShowIntroduction(false);
    setIntroductionCompleted(true);
  };

  useEffect(() => {
    const initializeMocktest = async () => {
      if (!user?.id || !practiceId) return;

      try {
        setIsInitializing(true);
        await localStorage.setItem("userId", `${user?.id}`);

        // Initialize mocktest with logging
        const attemptData = await mocktestService.getOrCreateMocktestAttempt(
          practiceId,
          user.id
        );

        if (attemptData) {
          setCurrentLogId(attemptData.logId);
          setAttemptNumber(attemptData.attemptNumber);
          setAttemptType(attemptData.type);
          setmockTest(attemptData.mocktest);

          // Load questions for this mocktest
          const uri = server.uri + "questionsBysection/" + practiceId;
          const questionsData = await getRequest(uri);
          if (questionsData) {
            settotalQuestions(questionsData);
          }

          // If resuming, set current section and question and skip introduction
          if (attemptData.type === "resume") {
            setsection(attemptData.currentSection);
            setCurrentQuestion(attemptData.currentQuestion);
            setShowIntroduction(false);
            setIntroductionCompleted(true);

            // Check if we need to start the listening timer when resuming
            if (attemptData.currentSection === 2 && questionsData?.[2]) {
              const currentQuestionData =
                questionsData[2][attemptData.currentQuestion];
              if (
                currentQuestionData &&
                !isSummarizeSpokenText(currentQuestionData)
              ) {
                setListeningTimerStarted(true);
              }
            }

            // Note: Auto-start will be handled by useEffect once dependencies are ready

            toast.info(
              `Resuming Attempt ${attemptData.attemptNumber} - Continuing where you left off`
            );
          } else {
            toast.success(`Starting Attempt ${attemptData.attemptNumber}`);
            setIsInitializing(false); // End loading for new attempts
          }
        }
      } catch (error) {
        console.error("Error initializing mocktest:", error);
        toast.error("Failed to initialize test. Please try again.");
        setIsInitializing(false);
      }
    };

    initializeMocktest();
  }, [user?.id, practiceId]);

  // This useEffect was moved after startTest definition to avoid reference error

  // Start the test
  const startTest = useCallback(async () => {
    try {
      const currentTime = new Date().toISOString();
      const sectionNames = ["speaking", "reading", "listening"];
      const currentSectionName = sectionNames[section];

      // Set timer based on section
      if (section == 0) {
        setTimeLeft(Number(mockTest?.speakingQuestionsDuration) * 60);
      }
      if (section == 1) {
        setTimeLeft(Number(mockTest?.readingQuestionsDuration) * 60);
      }
      if (section == 2) {
        // For listening section, only start timer if we're past Summarize Spoken Text questions
        const questionsForSection = totalQuestions?.[section] || [];
        const currentQuestionData = questionsForSection[currentQuestion];

        if (
          currentQuestionData &&
          !isSummarizeSpokenText(currentQuestionData)
        ) {
          // Start the 20-minute timer for non-Summarize questions
          setTimeLeft(Number(mockTest?.listeningQuestionsDuration) * 60);
          setListeningTimerStarted(true);
        } else {
          // Don't start timer for Summarize Spoken Text questions
          setTimeLeft(0);
          setListeningTimerStarted(false);
        }
      }

      // Update section start time
      setSectionTimes((prev) => ({
        ...prev,
        [currentSectionName]: {
          ...prev[currentSectionName],
          startTime: currentTime,
        },
      }));

      if (totalQuestions?.[section]) {
        setQuestions(totalQuestions?.[section]);
        setTestStarted(true);

        if (!testStartTime) {
          setTestStartTime(currentTime);
        }

        // Update log with section start
        if (currentLogId) {
          await mocktestService.updateMocktestLog(currentLogId, {
            currentSection: section,
            currentQuestion: currentQuestion,
            status: "in_progress",
            [`${currentSectionName}_startTime`]: currentTime,
          });
        }

        if (localStorage.getItem("curentNumber")) {
          const index = Number(localStorage.getItem("curentNumber"));
          setCurrentQuestion(index);
        } else {
          setCurrentQuestion(0);
        }
      } else {
        toast.error("Error loading questions");
      }
    } catch (error) {
      console.error("Error starting test section:", error);
      toast.error("Failed to start test section");
    }
  }, [
    section,
    mockTest,
    totalQuestions,
    currentQuestion,
    currentLogId,
    testStartTime,
    setSectionTimes,
  ]);

  // Handle resume scenario - auto-start test when all dependencies are ready
  useEffect(() => {
    if (
      attemptType === "resume" &&
      introductionCompleted &&
      !testStarted &&
      totalQuestions?.length > 0 &&
      totalQuestions[section]?.length > 0
    ) {
      // Auto-start the test for resume scenario
      const timer = setTimeout(() => {
        startTest();
        setIsInitializing(false); // End loading once test starts
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [
    attemptType,
    introductionCompleted,
    testStarted,
    totalQuestions,
    section,
    startTest,
  ]);

  // Modified to update parent component state
  // In MockTest.jsx, update the handleUploadAnswer function
  const handleUploadAnswer = async (answer) => {
    try {
      const uri = server.uri + "answers";

      if (!answer) {
        console.log("No answer to upload");
        return;
      }

      // Ensure proper data structure for different question types
      const enhancedAnswer = {
        ...answer,
        mockTestId: practiceId,
        mocktestId: practiceId, // Include both for compatibility
        attemptNumber: attemptNumber,
        userId: user?.id || localStorage.getItem("userId"),
      };

      // Special handling for summarize spoken text
      if (
        answer.type?.toLowerCase().includes("summarize") ||
        answer.additionalProps?.scoreResponse
      ) {
        enhancedAnswer.pteScores = answer.additionalProps?.scoreResponse;
      }

      // Special handling for fill-in-blanks
      if (
        answer.type?.toLowerCase().includes("fill") ||
        answer.type === "fill-in-blanks"
      ) {
        // Ensure correct answer structure
        enhancedAnswer.type = "listening：fill in the blanks";
      }

      console.log("Uploading enhanced answer:", enhancedAnswer);
      const sendAnswer = await postRequest(uri, enhancedAnswer);

      if (sendAnswer) {
        toast.success("Answer uploaded successfully");
        setAnswerSubmitted(true);
        setcurrentAnswer(enhancedAnswer);
        return sendAnswer;
      } else {
        toast.error("Invalid Answer");
        return null;
      }
    } catch (error) {
      console.error("Error in handleUploadAnswer:", error);
      toast.error("Failed to upload answer");
      throw error;
    }
  };

  const handleAnswerProcessing = (status) => {
    // Only disable Next button if in speaking section and processing
    if (section === 0) {
      setIsProcessing(status);
      setcanGoNext(!status);
    } else {
      // For other sections, don't block the Next button
      setIsProcessing(false);
      setcanGoNext(true);
    }
  };

  // Modified to check the parent component flag and update logs
  const nextQuestion = async () => {
    // Check if we're in the speaking section and currently processing
    if (section === 0 && isProcessing) {
      setwarningMessage(true);
      return;
    }

    try {
      // Only upload answer if it hasn't been submitted yet
      if (currentAnswer && !answerSubmitted) {
        const uri = server.uri + "answers";
        // Add dynamic mockTestId to answer
        const answerWithMockTestId = {
          ...currentAnswer,
          mockTestId: practiceId,
          attemptNumber: attemptNumber,
        };
        const sendAnswer = await postRequest(uri, answerWithMockTestId);

        if (!sendAnswer) {
          toast.error("Invalid Answer");
          return;
        }

        toast.success("Answer uploaded successfully");
      }

      // Reset answer submitted status for the next question
      setAnswerSubmitted(false);

      // Update log with current progress
      if (currentLogId) {
        await mocktestService.updateMocktestLog(currentLogId, {
          currentSection: section,
          currentQuestion: currentQuestion + 1,
          status: "in_progress",
        });
      }

      // Navigation logic with section completion tracking
      if (currentQuestion === questions?.length - 1) {
        // Mark current section as completed
        const sectionNames = ["speaking", "reading", "listening"];
        const currentSectionName = sectionNames[section];
        const sectionEndTime = new Date().toISOString();

        // Calculate time spent in this section
        const timeSpent = sectionTimes[currentSectionName].startTime
          ? Math.floor(
              (new Date(sectionEndTime) -
                new Date(sectionTimes[currentSectionName].startTime)) /
                1000
            )
          : 0;

        setSectionTimes((prev) => ({
          ...prev,
          [currentSectionName]: {
            ...prev[currentSectionName],
            timeSpent: timeSpent,
          },
        }));

        if (section < 2) {
          // Move to next section
          setshowPopup(false);
          setsection(section + 1);
          setTestStarted(false);
          setshowNextModal(false);

          // Update log with section completion
          if (currentLogId) {
            await mocktestService.updateMocktestLog(currentLogId, {
              currentSection: section + 1,
              currentQuestion: 0,
              [`${currentSectionName}_completed`]: true,
              [`${currentSectionName}_timeSpent`]: timeSpent,
            });
          }
        } else {
          // Test completed
          await completeTest();
          setshowNextModal(false);
          setshowPopup(false);
          setShowTestCompleteModal(true);
        }
      } else {
        const nextQuestionIndex = currentQuestion + 1;
        setCurrentQuestion(nextQuestionIndex);
        setshowPopup(false);
        setshowNextModal(false);
        setcanGoNext(true);
        setcurrentAnswer(null);

        // Handle listening timer logic for the next question
        if (section === 2 && questions?.[nextQuestionIndex]) {
          const nextQuestion = questions[nextQuestionIndex];

          if (isSummarizeSpokenText(nextQuestion)) {
            // If moving to a Summarize question, don't show timer (it has its own internal timer)
            // Keep the timer state but don't display it
          } else if (!listeningTimerStarted) {
            // Start the 20-minute timer for the first non-Summarize question
            setTimeLeft(Number(mockTest?.listeningQuestionsDuration) * 60);
            setListeningTimerStarted(true);
          }
          // If timer was already started and this is not a Summarize question, timer continues
        }
      }
    } catch (error) {
      console.error("Error in nextQuestion:", error);
      toast.error("Failed to upload answer");
    }
  };

  useEffect(() => {
    if (currentAnswer) {
      const array = answers;
      array.push(currentAnswer);
    }
  }, [currentAnswer]);

  // Update the warning message modal content based on current state
  const getWarningMessage = () => {
    if (isProcessing) {
      if (section === 0) {
        // Speaking section
        return "Your speaking response is being processed. Please wait a moment before moving to the next question.";
      }
      return "Processing in progress. Please wait...";
    }
    return "Wait for completion of question before moving to the next question";
  };

  // Complete test functionality
  const completeTest = async () => {
    try {
      if (!currentLogId) return;

      const endTime = new Date().toISOString();
      const totalTimeSpent = testStartTime
        ? Math.floor((new Date(endTime) - new Date(testStartTime)) / 1000)
        : 0;

      // Calculate final score (you can modify this logic based on your scoring system)
      const finalScore = 0; // This should be calculated based on answers

      const finalData = {
        score: finalScore,
        sections: {
          speaking: {
            completed: true,
            timeSpent: sectionTimes.speaking.timeSpent,
          },
          reading: {
            completed: true,
            timeSpent: sectionTimes.reading.timeSpent,
          },
          listening: {
            completed: true,
            timeSpent: sectionTimes.listening.timeSpent,
          },
        },
        totalTimeSpent: totalTimeSpent,
      };

      await mocktestService.completeTest(currentLogId, finalData);

      // Store completion data for result page
      localStorage.setItem("completedTestId", practiceId);
      localStorage.setItem("completedAttemptNumber", attemptNumber.toString());

      toast.success("Test completed successfully!");
    } catch (error) {
      console.error("Error completing test:", error);
      toast.error("Failed to complete test");
    }
  };

  // Save & Exit the test
  const saveAndExit = async () => {
    try {
      if (!currentLogId) {
        toast.error("No active session found");
        return;
      }

      const currentTime = new Date().toISOString();

      // Calculate time spent so far
      let totalTimeSpent = 0;
      if (testStartTime) {
        totalTimeSpent = Math.floor(
          (new Date(currentTime) - new Date(testStartTime)) / 1000
        );
      }

      const currentData = {
        section: section,
        question: currentQuestion,
        sections: sectionTimes,
        totalTimeSpent: totalTimeSpent,
      };

      await mocktestService.saveAndExit(currentLogId, currentData);

      toast.success("Test progress saved successfully!");

      // Navigate back to home or test selection
      setTimeout(() => {
        navigate("/");
      }, 1500);
    } catch (error) {
      console.error("Error saving test progress:", error);
      toast.error("Failed to save test progress");
    }
  };

  // Timer effect (countdown)
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(timer);
    } else if (timeLeft === 0 && testStarted) {
      // When timer reaches 0, automatically move to next section
      handleTimeUp();
    }
  }, [timeLeft, testStarted]);

  // Handle when time is up for a section
  const handleTimeUp = async () => {
    try {
      // Mark current section as completed
      const sectionNames = ["speaking", "reading", "listening"];
      const currentSectionName = sectionNames[section];
      const sectionEndTime = new Date().toISOString();

      // Calculate time spent in this section
      const timeSpent = sectionTimes[currentSectionName].startTime
        ? Math.floor(
            (new Date(sectionEndTime) -
              new Date(sectionTimes[currentSectionName].startTime)) /
              1000
          )
        : 0;

      setSectionTimes((prev) => ({
        ...prev,
        [currentSectionName]: {
          ...prev[currentSectionName],
          timeSpent: timeSpent,
        },
      }));

      if (section < 2) {
        // Move to next section
        setsection(section + 1);
        setTestStarted(false);
        setCurrentQuestion(0);

        // Update log with section completion
        if (currentLogId) {
          await mocktestService.updateMocktestLog(currentLogId, {
            currentSection: section + 1,
            currentQuestion: 0,
            [`${currentSectionName}_completed`]: true,
            [`${currentSectionName}_timeSpent`]: timeSpent,
          });
        }

        toast.info(
          `Time's up for ${dataForParams[section]}! Moving to next section.`
        );
      } else {
        // Test completed
        await completeTest();
        setShowTestCompleteModal(true);
        toast.success("Test completed! Time's up for all sections.");
      }
    } catch (error) {
      console.error("Error handling time up:", error);
      toast.error("Failed to process section completion");
    }
  };

  // Get the current component for the question
  const Component = ComponentData?.find(
    (item) => item.categoryId == questions?.[currentQuestion]?.categoryId
  )?.Component;

  // Show loading screen while initializing
  if (isInitializing) {
    return (
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "#f4f0ff",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          zIndex: 1000,
        }}
      >
        <div
          style={{
            backgroundColor: "white",
            borderRadius: "20px",
            padding: "48px 64px",
            maxWidth: "500px",
            width: "90%",
            textAlign: "center",
            boxShadow: "0 20px 60px rgba(20, 3, 66, 0.15)",
            border: "1px solid rgba(20, 3, 66, 0.1)",
          }}
        >
          {/* Animated Loading Spinner */}
          <div
            style={{
              width: "80px",
              height: "80px",
              margin: "0 auto 32px",
              position: "relative",
            }}
          >
            <div
              style={{
                width: "100%",
                height: "100%",
                border: "6px solid #f4f0ff",
                borderTop: "6px solid #140342",
                borderRadius: "50%",
                animation: "spin 1.2s linear infinite",
              }}
            />
            <div
              style={{
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                width: "20px",
                height: "20px",
                backgroundColor: "#140342",
                borderRadius: "50%",
                animation: "pulse 1.5s ease-in-out infinite",
              }}
            />
          </div>

          <h2
            style={{
              color: "#140342",
              fontSize: "28px",
              fontWeight: "700",
              marginBottom: "16px",
              letterSpacing: "-0.5px",
            }}
          >
            {attemptType === "resume"
              ? "Resuming Your Test"
              : "Initializing Test"}
          </h2>

          <p
            style={{
              color: "#64748b",
              fontSize: "16px",
              margin: 0,
              lineHeight: "1.6",
              fontWeight: "400",
            }}
          >
            {attemptType === "resume"
              ? "Loading your previous progress and preparing to continue where you left off. Please wait a moment..."
              : "Setting up your test environment and loading questions. This will just take a moment..."}
          </p>

          {/* Progress dots */}
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              gap: "8px",
              marginTop: "32px",
            }}
          >
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                style={{
                  width: "8px",
                  height: "8px",
                  borderRadius: "50%",
                  backgroundColor: "#140342",
                  animation: `pulse-dot 1.4s ease-in-out ${i * 0.2}s infinite`,
                }}
              />
            ))}
          </div>
        </div>

        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
            @keyframes pulse {
              0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
              50% { opacity: 0.5; transform: translate(-50%, -50%) scale(0.8); }
            }
            @keyframes pulse-dot {
              0%, 100% { opacity: 0.3; transform: scale(0.8); }
              50% { opacity: 1; transform: scale(1.2); }
            }
          `}
        </style>
      </div>
    );
  }

  // Show introduction flow if not completed
  if (showIntroduction && !introductionCompleted) {
    return <CompleteIntroFlow onComplete={handleIntroductionComplete} />;
  }

  return (
    <div>
      {/* Header Section */}
      <Box
        position="sticky"
        sx={{
          top: 0,
          background: "linear-gradient(135deg, #140342 0%, #1e0a5c 100%)",
          width: "100%",
          margin: 0,
          padding: "0 40px 0 40px",
          zIndex: 1000,
          boxShadow: "0 4px 20px rgba(20, 3, 66, 0.3)",
        }}
      >
        <Toolbar
          sx={{
            justifyContent: "space-between",
            padding: "0 200px 0 200px",
            minHeight: "70px",
          }}
        >
          <Box display="flex" alignItems="center" gap={2}>
            <Typography
              variant="h5"
              sx={{ color: "white", fontWeight: "700", letterSpacing: "0.5px" }}
            >
              PTE Practice Test
            </Typography>
            {testStarted && (
              <Typography
                variant="body2"
                sx={{
                  color: "#f4f0ff",
                  backgroundColor: "rgba(255,255,255,0.1)",
                  padding: "4px 12px",
                  borderRadius: "12px",
                  fontSize: "0.75rem",
                }}
              >
                Section {section + 1}: {dataForParams[section]} | Attempt{" "}
                {attemptNumber}
              </Typography>
            )}
          </Box>
          {testStarted && (
            <Box sx={{ textAlign: "right", color: "white" }}>
              {/* Show timer for Reading section (section 1) and Listening section (section 2) */}
              {/* For listening section, only show timer if it has started (after Summarize Spoken Text) */}
              {(section === 1 ||
                (section === 2 &&
                  listeningTimerStarted &&
                  timeLeft > 0 &&
                  questions?.[currentQuestion] &&
                  !isSummarizeSpokenText(questions[currentQuestion]))) && (
                <Timer totalTime={timeLeft} />
              )}
              <Typography
                variant="body2"
                sx={{ color: "#f4f0ff", fontSize: "0.85rem" }}
              >
                {questions?.length - currentQuestion} Questions Remaining
              </Typography>
            </Box>
          )}
        </Toolbar>
      </Box>

      {/* Body Section */}
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-start",
          alignItems: "center",
          height: "calc(100vh - 140px)",
          backgroundColor: "#f5f5f5",
          paddingTop: 5,
          paddingBottom: 5,
          overflowY: "scroll",
        }}
      >
        {testStarted ? (
          <>
            <ProgressBar
              total={questions?.length}
              current={currentQuestion + 1}
            />
            <Box sx={{ marginTop: 3, width: "100%", maxWidth: "90%" }}>
              {questions?.[currentQuestion] &&
                (Component ? (
                  <Component
                    question={questions?.[currentQuestion]}
                    onNext={() => setshowPopup(true)}
                    setAnswer={(item) => {
                      console.log("Setting new answer:", item);
                      setcurrentAnswer((prev) => {
                        console.log("Previous answer:", prev);
                        return item;
                      });
                    }}
                    setGoNext={() => setcanGoNext(true)}
                    onProcessingStateChange={handleAnswerProcessing}
                    handleUploadAnswer={handleUploadAnswer}
                    onSpeakingStateChange={(isSpeaking) =>
                      setIsSpeaking(isSpeaking)
                    }
                  />
                ) : (
                  <div>Loading question content...</div>
                ))}
            </Box>
          </>
        ) : (
          <Box
            sx={{
              textAlign: "center",
              backgroundColor: "white",
              padding: 6,
              borderRadius: 3,
              border: "2px solid #140342",
              maxWidth: 600,
              mx: "auto",
              mt: 4,
            }}
          >
            <Typography
              variant="h4"
              sx={{ color: "#140342", fontWeight: 700, mb: 2 }}
            >
              Section {section + 1}
            </Typography>
            <Typography variant="h5" sx={{ color: "#140342", mb: 3 }}>
              {dataForParams[section]}
            </Typography>
            <Typography variant="body1" sx={{ color: "#666", mb: 4 }}>
              Click the button below to begin this section of the test
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={startTest}
              sx={{
                backgroundColor: "#140342",
                color: "white",
                fontWeight: "700",
                fontSize: "1.1rem",
                px: 4,
                py: 1.5,
                "&:hover": {
                  backgroundColor: "#1e0a5c",
                },
              }}
            >
              Start Section
            </Button>
          </Box>
        )}
      </Box>

      {/* Time's Up Modal */}
      <Modal
        open={showPopup}
        onClose={() => setshowPopup(false)}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: "white",
            padding: 3,
            borderRadius: 2,
            boxShadow: 24,
            minWidth: 300,
          }}
        >
          <Typography id="modal-title" variant="h6" component="h2">
            Time&apos;s Up
          </Typography>
          <Typography id="modal-description" sx={{ mt: 2, mb: 3 }}>
            Your time for this question has ended. Please proceed to the next
            question.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={async () => await nextQuestion()}
            sx={{ marginTop: 2 }}
            fullWidth
          >
            Next Question
          </Button>
        </Box>
      </Modal>

      {/* Rest of component remains the same */}
      {/* Next Question Confirmation Modal */}
      <Modal
        open={showNextModal}
        onClose={() => setshowNextModal(false)}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: "white",
            padding: 3,
            borderRadius: 2,
            boxShadow: 24,
            minWidth: 300,
          }}
        >
          <Typography id="modal-title" variant="h6" component="h2">
            Go To Next Question
          </Typography>
          <Typography id="modal-description" sx={{ mt: 2, mb: 3 }}>
            Are you sure you want to move to the next question?
          </Typography>
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Button
              variant="contained"
              color="primary"
              onClick={async () => await nextQuestion()}
              sx={{ marginRight: 2 }}
            >
              Yes
            </Button>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => setshowNextModal(false)}
            >
              No
            </Button>
          </Box>
        </Box>
      </Modal>

      {/* Processing Warning Modal */}
      <Modal
        open={warningMessage}
        onClose={() => setwarningMessage(false)}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: "white",
            padding: 3,
            borderRadius: 2,
            boxShadow: 24,
            minWidth: 300,
          }}
        >
          <Typography id="modal-title" variant="h6" component="h2">
            Please Wait
          </Typography>
          <Typography id="modal-description" sx={{ mt: 2, mb: 3 }}>
            {getWarningMessage()}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => setwarningMessage(false)}
            fullWidth
          >
            OK
          </Button>
        </Box>
      </Modal>

      {/* Test Complete Modal */}
      <Modal
        open={showTestCompleteModal}
        aria-labelledby="test-complete-modal-title"
        aria-describedby="test-complete-modal-description"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: "white",
            padding: 4,
            borderRadius: 2,
            boxShadow: 24,
            width: "90%",
            maxWidth: "400px",
          }}
        >
          <Typography
            id="test-complete-modal-title"
            variant="h5"
            component="h2"
            sx={{ mb: 2, textAlign: "center", color: "primary.main" }}
          >
            Test Completed!
          </Typography>
          <Typography
            id="test-complete-modal-description"
            sx={{ mt: 2, mb: 3, textAlign: "center" }}
          >
            You have successfully completed all sections of the test. Click
            below to view your results.
          </Typography>
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            <Button
              variant="contained"
              color="primary"
              onClick={() =>
                navigate(
                  `/mockTest/result?testId=${practiceId}&attempt=${attemptNumber}`
                )
              }
              sx={{
                minWidth: "200px",
                py: 1.5,
              }}
            >
              Proceed to Results
            </Button>
          </Box>
        </Box>
      </Modal>

      {/* Footer Section */}
      <Box
        sx={{
          position: "fixed",
          bottom: 0,
          left: 0,
          right: 0,
          background: "linear-gradient(135deg, #140342 0%, #1e0a5c 100%)",
          padding: "20px 40px 20px 40px",
          zIndex: 1000,
          boxShadow: "0 -4px 20px rgba(20, 3, 66, 0.3)",
        }}
      >
        {testStarted && (
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>
              <Button
                variant="outlined"
                size="large"
                onClick={saveAndExit}
                sx={{
                  color: "white",
                  borderColor: "white",
                  fontWeight: "600",
                  "&:hover": {
                    borderColor: "#f4f0ff",
                    backgroundColor: "rgba(255,255,255,0.1)",
                  },
                }}
              >
                Save & Exit
              </Button>
            </Grid>
            <Grid item display="flex" alignItems="center" gap={2}>
              <Typography variant="body2" sx={{ color: "#f4f0ff" }}>
                Question {currentQuestion + 1} of {questions?.length}
              </Typography>
              <Button
                variant="contained"
                size="large"
                sx={{
                  width: "200px",
                  position: "relative",
                  overflow: "hidden",
                  backgroundColor: "white",
                  color: "#140342",
                  fontWeight: "700",
                  "&:hover": {
                    backgroundColor: "#f4f0ff",
                  },
                  "&:disabled": {
                    backgroundColor: "rgba(255,255,255,0.3)",
                    color: "rgba(255,255,255,0.7)",
                  },
                }}
                onClick={() =>
                  canGoNext ? setshowNextModal(true) : setwarningMessage(true)
                }
                disabled={section === 0 && isSpeaking}
              >
                {isProcessing ? (
                  <>
                    <Typography
                      component="span"
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        opacity: 0.8,
                      }}
                    >
                      Processing...
                    </Typography>
                  </>
                ) : (
                  "Next Question"
                )}
              </Button>
            </Grid>
          </Grid>
        )}
      </Box>
    </div>
  );
}

export default MockTest;
