import { useState, useEffect, useCallback } from "react";
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Grid,
  Container,
  styled,
} from "@mui/material";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import PropTypes from "prop-types";
import PTEScoreLoader from "./PTEScoreLoader";
import ResultSection from "./ResultSection";
import PTECertificate from "./PTECertificate";
import { server } from "../../api/services/server";
import { useNavigate } from "react-router-dom";
import { PTECrossSectionalScoring } from "../../utils/pteScoring";

const StyledBox = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4),
}));

const StyledCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(4),
  borderRadius: theme.spacing(1),
}));

const StyledTypography = styled(Typography)(() => ({
  fontWeight: 700,
  letterSpacing: "-0.5px",
  color: "#140342",
}));

const StyledSubTypography = styled(Typography)(() => ({
  fontWeight: 500,
  fontSize: "1.1rem",
  color: "#140342",
}));

const TabPanel = ({ children, value, index }) => (
  <div hidden={value !== index} style={{ padding: "24px" }}>
    {value === index && children}
  </div>
);

TabPanel.propTypes = {
  children: PropTypes.node,
  value: PropTypes.number.isRequired,
  index: PropTypes.number.isRequired,
};

const ScoreDisplay = styled(Box)(({ theme }) => ({
  textAlign: "center",
  padding: theme.spacing(3),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[1],
  transition: "transform 0.2s ease-in-out",
  "&:hover": {
    transform: "translateY(-4px)",
    boxShadow: theme.shadows[3],
  },
}));

// Helper function to safely extract score values
const getScoreValue = (scoreData) => {
  if (typeof scoreData === "object" && scoreData !== null) {
    return scoreData.score || 0;
  }
  return scoreData || 0;
};

// Modified function to treat content score consistently with other scores
const calculateOverallScore = (scores) => {
  let total = 0;
  let count = 0;

  // Add pronunciation if available
  if (scores.pronunciation !== undefined) {
    total += scores.pronunciation;
    count++;
  }

  // Add fluency if available
  if (scores.fluency !== undefined) {
    total += scores.fluency;
    count++;
  }

  // Add content score if available - now treating it consistently
  if (scores.content !== undefined) {
    total += scores.content;
    count++;
  }

  // Calculate average, or return 0 if no scores available
  return count > 0 ? Math.round(total / count) : 0;
};

// Function to calculate overall writing score (summarize written text)
const calculateOverallWritingScore = (scores) => {
  let total = 0;
  let count = 0;

  // Add all writing components
  ["content", "form", "grammar", "vocabulary"].forEach((component) => {
    if (scores[component] !== undefined) {
      total += scores[component];
      count++;
    }
  });

  // Calculate average, or return 0 if no scores available
  return count > 0 ? Math.round(total / count) : 0;
};

// Function to calculate overall essay score
const calculateOverallEssayScore = (scores) => {
  let total = 0;
  let count = 0;

  // Add all essay components
  [
    "content",
    "form",
    "grammar",
    "spelling",
    "vocabularyRange",
    "generalLinguisticRange",
    "developmentStructureCoherence",
  ].forEach((component) => {
    if (scores[component] !== undefined) {
      total += scores[component];
      count++;
    }
  });

  // Calculate average, or return 0 if no scores available
  return count > 0 ? Math.round(total / count) : 0;
};

const ResultComponent = ({ mockTestId, userId, attemptNumber }) => {
  const [value, setValue] = useState(0);
  const [speakingScores, setSpeakingScores] = useState([]);
  const [writingScores, setWritingScores] = useState([]);
  const [essayScores, setEssayScores] = useState([]);
  const [showDetailedScores, setShowDetailedScores] = useState(false);
  const [showWritingDetailedScores, setShowWritingDetailedScores] =
    useState(false);
  const [showEssayDetailedScores, setShowEssayDetailedScores] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [selectedWritingQuestion, setSelectedWritingQuestion] = useState(null);
  const [selectedEssayQuestion, setSelectedEssayQuestion] = useState(null);
  const [loading, setLoading] = useState(true);
  const [taskTypeStats, setTaskTypeStats] = useState({});
  const [readingScores, setReadingScores] = useState([]);
  const [listeningScores, setListeningScores] = useState([]);
  const [attemptHistory, setAttemptHistory] = useState([]);
  const [overallScores, setOverallScores] = useState({
    speaking: 0,
    writing: 0,
    reading: 0,
    listening: 0,
    overall: 0,
  });
  const [readingResults, setReadingResults] = useState(null);
  const [listeningResults, setListeningResults] = useState(null);
  const [crossSectionalScores, setCrossSectionalScores] = useState({
    speaking: 0,
    writing: 0,
    reading: 0,
    listening: 0,
    overall: 0,
  });
  const [allMockTestQuestions, setAllMockTestQuestions] = useState({
    speaking: [],
    writing: [],
    reading: [],
    listening: [],
  });
  const navigate = useNavigate();

  // Add this function to calculate writing score out of 90
  const calculateWritingOverallScore = () => {
    let totalWritingScore = 0;
    let maxWritingScore = 0;

    // Add summarize written text scores
    writingScores.forEach((score) => {
      const content = getScoreValue(score.pteScores?.content);
      const form = getScoreValue(score.pteScores?.form);
      const grammar = getScoreValue(score.pteScores?.grammar);
      const vocabulary = getScoreValue(score.pteScores?.vocabulary);

      totalWritingScore += content + form + grammar + vocabulary;
      maxWritingScore +=
        (score.pteScores?.content?.maxScore || 2) +
        (score.pteScores?.form?.maxScore || 2) +
        (score.pteScores?.grammar?.maxScore || 2) +
        (score.pteScores?.vocabulary?.maxScore || 2);
    });

    // Add essay scores
    essayScores.forEach((score) => {
      const content = getScoreValue(score.pteScores?.content);
      const form = getScoreValue(score.pteScores?.form);
      const grammar = getScoreValue(score.pteScores?.grammar);
      const spelling = getScoreValue(score.pteScores?.spelling);
      const vocabularyRange = getScoreValue(score.pteScores?.vocabularyRange);
      const generalLinguisticRange = getScoreValue(
        score.pteScores?.generalLinguisticRange
      );
      const developmentStructureCoherence = getScoreValue(
        score.pteScores?.developmentStructureCoherence
      );

      totalWritingScore +=
        content +
        form +
        grammar +
        spelling +
        vocabularyRange +
        generalLinguisticRange +
        developmentStructureCoherence;
      maxWritingScore +=
        (score.pteScores?.content?.maxScore || 3) +
        (score.pteScores?.form?.maxScore || 2) +
        (score.pteScores?.grammar?.maxScore || 2) +
        (score.pteScores?.spelling?.maxScore || 2) +
        (score.pteScores?.vocabularyRange?.maxScore || 2) +
        (score.pteScores?.generalLinguisticRange?.maxScore || 2) +
        (score.pteScores?.developmentStructureCoherence?.maxScore || 2);
    });

    return maxWritingScore > 0
      ? Math.round((totalWritingScore / maxWritingScore) * 90)
      : 0;
  };

  useEffect(() => {
    const fetchResults = async () => {
      try {
        console.log("Fetching results for:", {
          mockTestId,
          userId,
          attemptNumber,
        });

        // Fetch all mock test questions first
        const allQuestionsResponse = await fetch(
          `${server.uri}questionsBysection/${mockTestId}`
        );
        const allQuestionsData = await allQuestionsResponse.json();

        if (allQuestionsData) {
          // Log the structure to understand the API response
          console.log("Raw API response structure:", allQuestionsData);

          // Group questions by their actual section from the question data
          const groupedQuestions = {
            speaking: [],
            writing: [],
            reading: [],
            listening: [],
          };

          // If allQuestionsData is an array of arrays (sections)
          if (Array.isArray(allQuestionsData)) {
            allQuestionsData.forEach((sectionQuestions, index) => {
              if (Array.isArray(sectionQuestions)) {
                sectionQuestions.forEach(question => {
                  const questionSection = question.section || question.category?.section;
                  if (questionSection && groupedQuestions[questionSection]) {
                    groupedQuestions[questionSection].push(question);
                  } else {
                    // Fallback to index-based assignment if section is not clear
                    const sectionNames = ['speaking', 'writing', 'reading', 'listening'];
                    const fallbackSection = sectionNames[index] || 'listening';
                    groupedQuestions[fallbackSection].push(question);
                  }
                });
              }
            });
          }

          setAllMockTestQuestions(groupedQuestions);
          console.log("Grouped mock test questions:", groupedQuestions);
        }

        const response = await fetch(`${server.uri}answers`);
        const data = await response.json();

        console.log("All answers data:", data);

        // Filter answers for current attempt with improved logic
        const filterCondition = (answer) => {
          const matchesUser = answer.userId === userId;
          const matchesMockTest =
            answer.mockTestId === mockTestId ||
            answer.mocktestId === mockTestId;
          const matchesAttempt =
            !attemptNumber || answer.attemptNumber === attemptNumber;

          console.log("Filter check:", {
            answer: answer.questionId,
            matchesUser,
            matchesMockTest,
            matchesAttempt,
            answerAttempt: answer.attemptNumber,
            targetAttempt: attemptNumber,
          });

          return matchesUser && matchesMockTest && matchesAttempt;
        };

        // Speaking answers
        const speakingAnswers = data.filter(
          (answer) =>
            filterCondition(answer) &&
            answer.pteScores &&
            (answer.section === "speaking" || answer.taskType) // Include taskType for speaking
        );

        console.log("Filtered speaking answers:", speakingAnswers);

        // Writing answers - filter for summarize-written-text
        const writingAnswers = data.filter(
          (answer) =>
            filterCondition(answer) &&
            answer.section === "writing" &&
            (answer.type === "summarize-written-text" ||
              answer.type === "summarize written text") &&
            answer.additionalProps?.scoreResponse
        );

        console.log("Filtered writing answers:", writingAnswers);

        // Essay answers - filter for essay-writing type
        const essayAnswers = data.filter(
          (answer) =>
            filterCondition(answer) &&
            answer.section === "writing" &&
            (answer.type === "essay-writing" ||
              answer.type === "essay writing") &&
            answer.additionalProps?.scoreResponse
        );

        console.log("Filtered essay answers:", essayAnswers);

        // Ensure all necessary score properties exist and convert similarity_score to content
        const normalizedSpeakingAnswers = speakingAnswers.map((answer) => {
          // Convert similarity_score to content score on the same scale as pronunciation and fluency
          const similarityScore = answer.pteScores.similarity_score || 0;
          const contentScore = Math.round(similarityScore * 100);

          const normalizedScores = {
            pronunciation: answer.pteScores.pronunciation || 0,
            fluency: answer.pteScores.fluency || 0,
            content: contentScore,
            // Keep the original for reference if needed
            similarity_score: similarityScore,
          };
          return {
            ...answer,
            pteScores: normalizedScores,
          };
        });

        // Process writing answers - use scoreResponse from additionalProps
        const normalizedWritingAnswers = writingAnswers.map((answer) => {
          return {
            ...answer,
            pteScores: answer.additionalProps.scoreResponse, // Use the AI scoring response directly
          };
        });

        // Process essay answers - use scoreResponse from additionalProps
        const normalizedEssayAnswers = essayAnswers.map((answer) => {
          return {
            ...answer,
            pteScores: answer.additionalProps.scoreResponse, // Use the AI scoring response directly
          };
        });

        const taskGroups = {};
        normalizedSpeakingAnswers.forEach((answer) => {
          const type = answer.taskType || "Other";
          if (!taskGroups[type]) {
            taskGroups[type] = [];
          }
          taskGroups[type].push(answer);
        });

        const typeStats = {};
        Object.entries(taskGroups).forEach(([type, answers]) => {
          const avgScores = {
            pronunciation: Math.round(
              answers.reduce((sum, a) => sum + a.pteScores.pronunciation, 0) /
                answers.length
            ),
            fluency: Math.round(
              answers.reduce((sum, a) => sum + a.pteScores.fluency, 0) /
                answers.length
            ),
            content: Math.round(
              answers.reduce((sum, a) => sum + a.pteScores.content, 0) /
                answers.length
            ),
            // Keep original similarity_score for reference
            similarity_score: parseFloat(
              (
                answers.reduce(
                  (sum, a) => sum + (a.pteScores.similarity_score || 0),
                  0
                ) / answers.length
              ).toFixed(2)
            ),
          };
          // Calculate overall as average of all three scores
          avgScores.overall = calculateOverallScore(avgScores);
          typeStats[type] = avgScores;
        });

        // Reading answers - improved filtering
        const readingAnswers = data.filter(
          (answer) => filterCondition(answer) && answer.section === "reading"
        );

        console.log("Filtered reading answers:", readingAnswers);
        console.log("Reading answers count:", readingAnswers.length);
        if (readingAnswers.length > 0) {
          console.log("Sample reading answer:", readingAnswers[0]);
        }

        // Listening answers - improved filtering
        const listeningAnswers = data.filter(
          (answer) => filterCondition(answer) && answer.section === "listening"
        );

        console.log("Filtered listening answers:", listeningAnswers);
        console.log("Listening answers count:", listeningAnswers.length);
        if (listeningAnswers.length > 0) {
          console.log("Sample listening answer:", listeningAnswers[0]);
        }

        setReadingScores(readingAnswers);
        setListeningScores(listeningAnswers);
        setSpeakingScores(normalizedSpeakingAnswers);
        setWritingScores(normalizedWritingAnswers);
        setEssayScores(normalizedEssayAnswers);
        setTaskTypeStats(typeStats);

        // Fetch attempt history with improved error handling
        try {
          const historyResponse = await fetch(
            `${server.uri}logs?filter=${encodeURIComponent(
              JSON.stringify({
                where: {
                  or: [{ mocktestId: mockTestId }, { mockTestId: mockTestId }],
                  userId: userId,
                },
                order: "attemptNumber ASC",
              })
            )}`
          );

          if (historyResponse.ok) {
            const historyData = await historyResponse.json();
            console.log("Attempt history data:", historyData);

            const completedLogs = (historyData || []).filter(
              (log) => log.status === "completed"
            );

            const uniqueAttempts = [];
            const seen = new Set();
            for (const log of completedLogs) {
              if (!seen.has(log.attemptNumber)) {
                uniqueAttempts.push(log);
                seen.add(log.attemptNumber);
              }
            }

            // Sort by attemptNumber ascending
            uniqueAttempts.sort((a, b) => a.attemptNumber - b.attemptNumber);
            console.log("Processed attempt history:", uniqueAttempts);
            setAttemptHistory(uniqueAttempts);
          }
        } catch (historyError) {
          console.error("Error fetching attempt history:", historyError);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error fetching results:", error);
        setLoading(false);
      }
    };

    if (mockTestId && userId) {
      fetchResults();
    }
  }, [mockTestId, userId, attemptNumber]);

  const calculateAverageScores = () => {
    if (speakingScores.length === 0) return null;

    const sum = speakingScores.reduce(
      (acc, curr) => ({
        pronunciation: acc.pronunciation + (curr.pteScores.pronunciation || 0),
        fluency: acc.fluency + (curr.pteScores.fluency || 0),
        content: acc.content + (curr.pteScores.content || 0),
      }),
      { pronunciation: 0, fluency: 0, content: 0 }
    );

    const averages = {
      pronunciation: Math.round(sum.pronunciation / speakingScores.length),
      fluency: Math.round(sum.fluency / speakingScores.length),
      content: Math.round(sum.content / speakingScores.length),
    };

    // Calculate overall as average of all three scores
    averages.overall = calculateOverallScore(averages);

    return averages;
  };

  const calculateWritingAverageScores = () => {
    if (writingScores.length === 0) return null;

    const sum = writingScores.reduce(
      (acc, curr) => ({
        content: acc.content + getScoreValue(curr.pteScores?.content),
        form: acc.form + getScoreValue(curr.pteScores?.form),
        grammar: acc.grammar + getScoreValue(curr.pteScores?.grammar),
        vocabulary: acc.vocabulary + getScoreValue(curr.pteScores?.vocabulary),
      }),
      { content: 0, form: 0, grammar: 0, vocabulary: 0 }
    );

    const averages = {
      content: Math.round(sum.content / writingScores.length),
      form: Math.round(sum.form / writingScores.length),
      grammar: Math.round(sum.grammar / writingScores.length),
      vocabulary: Math.round(sum.vocabulary / writingScores.length),
    };

    // Calculate overall as average of all four scores
    averages.overall = calculateOverallWritingScore(averages);

    return averages;
  };

  const calculateEssayAverageScores = () => {
    if (essayScores.length === 0) return null;

    const sum = essayScores.reduce(
      (acc, curr) => ({
        content: acc.content + getScoreValue(curr.pteScores?.content),
        form: acc.form + getScoreValue(curr.pteScores?.form),
        grammar: acc.grammar + getScoreValue(curr.pteScores?.grammar),
        spelling: acc.spelling + getScoreValue(curr.pteScores?.spelling),
        vocabularyRange:
          acc.vocabularyRange + getScoreValue(curr.pteScores?.vocabularyRange),
        generalLinguisticRange:
          acc.generalLinguisticRange +
          getScoreValue(curr.pteScores?.generalLinguisticRange),
        developmentStructureCoherence:
          acc.developmentStructureCoherence +
          getScoreValue(curr.pteScores?.developmentStructureCoherence),
      }),
      {
        content: 0,
        form: 0,
        grammar: 0,
        spelling: 0,
        vocabularyRange: 0,
        generalLinguisticRange: 0,
        developmentStructureCoherence: 0,
      }
    );

    const averages = {
      content: Math.round(sum.content / essayScores.length),
      form: Math.round(sum.form / essayScores.length),
      grammar: Math.round(sum.grammar / essayScores.length),
      spelling: Math.round(sum.spelling / essayScores.length),
      vocabularyRange: Math.round(sum.vocabularyRange / essayScores.length),
      generalLinguisticRange: Math.round(
        sum.generalLinguisticRange / essayScores.length
      ),
      developmentStructureCoherence: Math.round(
        sum.developmentStructureCoherence / essayScores.length
      ),
    };

    // Calculate overall as average of all seven scores
    averages.overall = calculateOverallEssayScore(averages);

    return averages;
  };

  // Helper function to create complete section data including unattempted questions
  const createCompleteSectionData = (section, attemptedQuestions) => {
    const allSectionQuestions = allMockTestQuestions[section] || [];
    const completeData = [];

    allSectionQuestions.forEach((question) => {
      const attemptedQuestion = attemptedQuestions.find(
        (attempted) => attempted.questionId === question.questionId
      );

      if (attemptedQuestion) {
        // Question was attempted - use actual data
        completeData.push({
          ...attemptedQuestion,
          attempted: true,
          questionData: question,
          section: section, // Override section to match the expected section
        });
      } else {
        // Question was not attempted - create zero score entry
        completeData.push({
          questionId: question.questionId,
          type:
            question.category?.name?.toLowerCase().replace(/\s+/g, "-") ||
            "unknown",
          score: 0,
          maxScore: question.maxScore || 1,
          percentage: 0,
          attempted: false,
          questionData: question,
          userAnswers: null,
          correctAnswers: question.correctAnswers || null,
          section: section, // Set section to match the expected section
        });
      }
    });

    return completeData;
  };

  // Callback functions to receive results from ResultSection components
  const handleReadingResults = (results) => {
    console.log("Reading results received:", results);
    setReadingResults(results);
  };

  const handleListeningResults = (results) => {
    console.log("Listening results received:", results);
    setListeningResults(results);
  };

  // Helper function to create complete question results including unattempted questions
  const createCompleteQuestionResults = useCallback(() => {
    const allQuestionResults = [];

    // Helper function to find user answer for a question
    const findUserAnswer = (questionId, section) => {
      switch (section) {
        case "speaking":
          return speakingScores.find(
            (score) => score.questionId === questionId
          );
        case "writing":
          return [...writingScores, ...essayScores].find(
            (score) => score.questionId === questionId
          );
        case "reading":
          return readingResults?.questions?.find(
            (q) => q.questionId === questionId
          );
        case "listening":
          return listeningResults?.questions?.find(
            (q) => q.questionId === questionId
          );
        default:
          return null;
      }
    };

    // Process all sections
    Object.keys(allMockTestQuestions).forEach((section) => {
      const sectionQuestions = allMockTestQuestions[section];

      sectionQuestions.forEach((question) => {
        const userAnswer = findUserAnswer(question.questionId, section);

        if (userAnswer) {
          // Question was attempted - use actual score
          let score = 0;
          let maxScore = 1;

          if (section === "speaking") {
            score = calculateOverallScore(userAnswer.pteScores);
            maxScore = 100;
          } else if (section === "writing") {
            score = userAnswer.additionalProps?.scoreResponse?.totalScore || 0;
            maxScore = userAnswer.additionalProps?.scoreResponse?.maxScore || 1;
          } else {
            score = userAnswer.score || 0;
            maxScore = userAnswer.maxScore || 1;
          }

          allQuestionResults.push({
            questionId: question.questionId,
            type:
              question.category?.name?.toLowerCase().replace(/\s+/g, "-") ||
              question.type ||
              "unknown",
            score: score,
            maxScore: maxScore,
            section: section,
            attempted: true,
          });
        } else {
          // Question was not attempted - score is 0
          allQuestionResults.push({
            questionId: question.questionId,
            type:
              question.category?.name?.toLowerCase().replace(/\s+/g, "-") ||
              question.type ||
              "unknown",
            score: 0,
            maxScore: question.maxScore || 1,
            section: section,
            attempted: false,
          });
        }
      });
    });

    return allQuestionResults;
  }, [
    speakingScores,
    writingScores,
    essayScores,
    readingResults,
    listeningResults,
    allMockTestQuestions,
  ]);

  // Add this useEffect to calculate overall scores using PTE cross-sectional methodology
  useEffect(() => {
    const calculateOverallScoresForAll = () => {
      // Initialize PTE scoring engine
      const pteScoring = new PTECrossSectionalScoring();

      // Get complete question results including unattempted questions
      const allQuestionResults = createCompleteQuestionResults();

      // Calculate cross-sectional scores
      const crossSectionalScores =
        pteScoring.aggregateScores(allQuestionResults);
      const overallScore =
        pteScoring.calculateOverallScore(crossSectionalScores);

      console.log("PTE Cross-Sectional Scores calculated:", {
        crossSectionalScores,
        overallScore,
        allQuestionResults: allQuestionResults.length,
      });

      // Update state with cross-sectional scores
      setCrossSectionalScores({
        ...crossSectionalScores,
        overall: overallScore,
      });

      setOverallScores({
        speaking: crossSectionalScores.speaking,
        writing: crossSectionalScores.writing,
        reading: crossSectionalScores.reading,
        listening: crossSectionalScores.listening,
        overall: overallScore,
      });
    };

    calculateOverallScoresForAll();
  }, [
    speakingScores,
    writingScores,
    essayScores,
    readingResults,
    listeningResults,
    allMockTestQuestions,
    createCompleteQuestionResults,
  ]);

  const handleTabChange = (event, newValue) => {
    setValue(newValue);
  };

  const handleQuestionClick = (question) => {
    setSelectedQuestion(question);
    setShowDetailedScores(true);
  };

  const handleWritingQuestionClick = (question) => {
    setSelectedWritingQuestion(question);
    setShowWritingDetailedScores(true);
  };

  const handleEssayQuestionClick = (question) => {
    setSelectedEssayQuestion(question);
    setShowEssayDetailedScores(true);
  };

  const averageScores = calculateAverageScores();
  const writingAverageScores = calculateWritingAverageScores();
  const essayAverageScores = calculateEssayAverageScores();

  const chartData = Object.entries(taskTypeStats).map(([type, scores]) => ({
    name: type,
    pronunciation: scores.pronunciation,
    fluency: scores.fluency,
    content: scores.content,
  }));

  // Chart data for writing (summarize written text)
  const writingChartData =
    writingScores.length > 0
      ? [
          {
            name: "Summarize Written Text",
            content: writingAverageScores?.content || 0,
            form: writingAverageScores?.form || 0,
            grammar: writingAverageScores?.grammar || 0,
            vocabulary: writingAverageScores?.vocabulary || 0,
          },
        ]
      : [];

  // Chart data for essays
  const essayChartData =
    essayScores.length > 0
      ? [
          {
            name: "Essay Writing",
            content: essayAverageScores?.content || 0,
            form: essayAverageScores?.form || 0,
            grammar: essayAverageScores?.grammar || 0,
            spelling: essayAverageScores?.spelling || 0,
            vocabularyRange: essayAverageScores?.vocabularyRange || 0,
            generalLinguisticRange:
              essayAverageScores?.generalLinguisticRange || 0,
            developmentStructureCoherence:
              essayAverageScores?.developmentStructureCoherence || 0,
          },
        ]
      : [];

  // Combined writing chart data
  const combinedWritingChartData = [
    ...(writingScores.length > 0 ? writingChartData : []),
    ...(essayScores.length > 0 ? essayChartData : []),
  ];

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100vh"
        marginTop="20px"
      >
        <PTEScoreLoader />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ marginTop: "140px" }}>
      <Paper elevation={3} sx={{ borderRadius: 2 }}>
        {/* Header with attempt information */}
        <Box sx={{ p: 3, borderBottom: "1px solid #e0e0e0" }}>
          <Typography
            variant="h4"
            sx={{ color: "#140342", fontWeight: 700, mb: 1 }}
          >
            PTE Practice Test Results
          </Typography>
          {attemptNumber && (
            <Typography variant="h6" sx={{ color: "#666", mb: 2 }}>
              Attempt {attemptNumber}
            </Typography>
          )}

          {/* Attempt History Selector */}
          {attemptHistory.length > 1 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" sx={{ color: "#666", mb: 1 }}>
                View Other Attempts:
              </Typography>
              <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap" }}>
                {attemptHistory.map((attempt) => (
                  <Button
                    key={attempt.attemptNumber}
                    variant={
                      attempt.attemptNumber === attemptNumber
                        ? "contained"
                        : "outlined"
                    }
                    size="small"
                    onClick={() => {
                      const currentUrl = new URL(window.location);
                      currentUrl.searchParams.set(
                        "attempt",
                        attempt.attemptNumber
                      );
                      navigate(currentUrl.pathname + currentUrl.search);
                    }}
                    sx={{
                      backgroundColor:
                        attempt.attemptNumber === attemptNumber
                          ? "#140342"
                          : "transparent",
                      color:
                        attempt.attemptNumber === attemptNumber
                          ? "white"
                          : "#140342",
                      borderColor: "#140342",
                      "&:hover": {
                        backgroundColor:
                          attempt.attemptNumber === attemptNumber
                            ? "#1e0a5c"
                            : "rgba(20, 3, 66, 0.1)",
                      },
                    }}
                  >
                    Attempt {attempt.attemptNumber}
                    {attempt.status === "completed" && " ✓"}
                  </Button>
                ))}
              </Box>
            </Box>
          )}
        </Box>

        <Tabs
          value={value}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
          sx={{
            "& .MuiTab-root": {
              fontWeight: 600,
              fontSize: "1.1rem",
              minWidth: 120,
              color: "#140342",
            },
            "& .Mui-selected": {
              color: "#140342 !important",
            },
            "& .MuiTabs-indicator": {
              backgroundColor: "#140342",
            },
          }}
        >
          <Tab label="Overall" />
          <Tab label="Speaking" />
          <Tab label="Writing" />
          <Tab label="Reading" />
          <Tab label="Listening" />
        </Tabs>

        {/* Overall Tab */}
        <TabPanel value={value} index={0}>
          <StyledCard>
            <CardContent>
              <StyledTypography variant="h5" gutterBottom>
                Overall Test Performance
              </StyledTypography>
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid item xs={12} md={6}>
                  <ScoreDisplay
                    sx={{
                      backgroundColor: "#140342",
                      color: "white",
                      minHeight: "150px",
                    }}
                  >
                    <Typography
                      variant="h2"
                      sx={{ fontWeight: 700, mb: 1, color: "white" }}
                    >
                      {overallScores.overall}
                    </Typography>
                    <Typography
                      variant="h5"
                      sx={{ fontWeight: 600, color: "white" }}
                    >
                      Overall Score
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1, opacity: 0.9 }}>
                      Out of 90
                    </Typography>
                  </ScoreDisplay>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <ScoreDisplay>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {overallScores.speaking}
                        </Typography>
                        <StyledSubTypography>Speaking</StyledSubTypography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={6}>
                      <ScoreDisplay>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {overallScores.writing}
                        </Typography>
                        <StyledSubTypography>Writing</StyledSubTypography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={6}>
                      <ScoreDisplay>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {overallScores.reading}
                        </Typography>
                        <StyledSubTypography>Reading</StyledSubTypography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={6}>
                      <ScoreDisplay>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {overallScores.listening}
                        </Typography>
                        <StyledSubTypography>Listening</StyledSubTypography>
                      </ScoreDisplay>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>

              {/* Performance Chart */}
              <StyledTypography variant="h6" gutterBottom sx={{ mt: 4 }}>
                PTE Cross-Sectional Skill Scores
              </StyledTypography>
              <Typography variant="body2" sx={{ mb: 3, color: "#666" }}>
                These scores reflect the official PTE cross-sectional scoring
                methodology where questions contribute to multiple skills.
              </Typography>
              <Box height={300}>
                <ResponsiveContainer>
                  <BarChart
                    data={[
                      { name: "Speaking", score: overallScores.speaking },
                      { name: "Writing", score: overallScores.writing },
                      { name: "Reading", score: overallScores.reading },
                      { name: "Listening", score: overallScores.listening },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.1} />
                    <XAxis
                      dataKey="name"
                      tick={{
                        fontSize: "1rem",
                        fontWeight: 500,
                        fill: "#140342",
                      }}
                    />
                    <YAxis
                      domain={[0, 90]}
                      tick={{
                        fontSize: "1rem",
                        fontWeight: 500,
                        fill: "#140342",
                      }}
                    />
                    <Tooltip
                      contentStyle={{
                        borderRadius: "8px",
                        fontWeight: 500,
                        fontSize: "1rem",
                        color: "#140342",
                      }}
                    />
                    <Bar dataKey="score" fill="#140342" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </StyledCard>

          {/* Certificate Section */}
          <StyledCard>
            <CardContent>
              <PTECertificate
                studentName="Practice Test Student" // You can get this from user data
                overallScore={overallScores.overall}
                speakingScore={overallScores.speaking}
                writingScore={overallScores.writing}
                readingScore={overallScores.reading}
                listeningScore={overallScores.listening}
                testDate={new Date().toLocaleDateString()}
                attemptNumber={attemptNumber || 1}
              />
            </CardContent>
          </StyledCard>
        </TabPanel>

        <TabPanel value={value} index={1}>
          {speakingScores.length > 0 ? (
            <>
              <StyledCard>
                <CardContent>
                  <StyledTypography variant="h5" gutterBottom>
                    Overall Speaking Performance
                  </StyledTypography>
                  {averageScores && (
                    <Grid container spacing={3}>
                      <Grid item xs={3}>
                        <ScoreDisplay>
                          <Typography
                            variant="h3"
                            sx={{ fontWeight: 700, color: "#140342" }}
                          >
                            {averageScores.pronunciation}
                          </Typography>
                          <StyledSubTypography>
                            Pronunciation
                          </StyledSubTypography>
                        </ScoreDisplay>
                      </Grid>
                      <Grid item xs={3}>
                        <ScoreDisplay>
                          <Typography
                            variant="h3"
                            sx={{ fontWeight: 700, color: "#140342" }}
                          >
                            {averageScores.fluency}
                          </Typography>
                          <StyledSubTypography>Fluency</StyledSubTypography>
                        </ScoreDisplay>
                      </Grid>
                      <Grid item xs={3}>
                        <ScoreDisplay>
                          <Typography
                            variant="h3"
                            sx={{ fontWeight: 700, color: "#140342" }}
                          >
                            {averageScores.content}
                          </Typography>
                          <StyledSubTypography>Content</StyledSubTypography>
                        </ScoreDisplay>
                      </Grid>
                      <Grid item xs={3}>
                        <ScoreDisplay>
                          <Typography
                            variant="h3"
                            sx={{ fontWeight: 700, color: "#140342" }}
                          >
                            {averageScores.overall}
                          </Typography>
                          <StyledSubTypography>Overall</StyledSubTypography>
                        </ScoreDisplay>
                      </Grid>
                    </Grid>
                  )}
                </CardContent>
              </StyledCard>

              <StyledCard>
                <CardContent>
                  <StyledTypography variant="h5" gutterBottom>
                    Performance by Task Type
                  </StyledTypography>
                  <Box height={400}>
                    <ResponsiveContainer>
                      <BarChart data={chartData}>
                        <CartesianGrid
                          strokeDasharray="3 3"
                          strokeOpacity={0.1}
                        />
                        <XAxis
                          dataKey="name"
                          tick={{
                            fontSize: "1rem",
                            fontWeight: 500,
                            fill: "#140342",
                          }}
                        />
                        <YAxis
                          tick={{
                            fontSize: "1rem",
                            fontWeight: 500,
                            fill: "#140342",
                          }}
                        />
                        <Tooltip
                          contentStyle={{
                            borderRadius: "8px",
                            fontWeight: 500,
                            fontSize: "1rem",
                            color: "#140342",
                          }}
                        />
                        <Legend
                          wrapperStyle={{
                            fontWeight: 500,
                            fontSize: "1.1rem",
                            color: "#140342",
                          }}
                        />
                        <Bar
                          dataKey="pronunciation"
                          fill="#140342"
                          name="Pronunciation"
                          radius={[4, 4, 0, 0]}
                        />
                        <Bar
                          dataKey="fluency"
                          fill="#281C63"
                          name="Fluency"
                          radius={[4, 4, 0, 0]}
                        />
                        <Bar
                          dataKey="content"
                          fill="#3D3584"
                          name="Content"
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </StyledCard>

              <StyledBox>
                <StyledTypography variant="h5" gutterBottom>
                  Question Details
                </StyledTypography>
                <TableContainer
                  component={Paper}
                  sx={{
                    borderRadius: 2,
                    boxShadow: 2,
                  }}
                >
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell
                          sx={{
                            fontWeight: 600,
                            fontSize: "1.1rem",
                            color: "#140342",
                          }}
                        >
                          Task Type
                        </TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            fontWeight: 600,
                            fontSize: "1.1rem",
                            color: "#140342",
                          }}
                        >
                          Pronunciation
                        </TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            fontWeight: 600,
                            fontSize: "1.1rem",
                            color: "#140342",
                          }}
                        >
                          Fluency
                        </TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            fontWeight: 600,
                            fontSize: "1.1rem",
                            color: "#140342",
                          }}
                        >
                          Content
                        </TableCell>
                        <TableCell
                          align="right"
                          sx={{
                            fontWeight: 600,
                            fontSize: "1.1rem",
                            color: "#140342",
                          }}
                        >
                          Actions
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {speakingScores.map((score, index) => (
                        <TableRow key={score.answerId || index}>
                          <TableCell
                            sx={{
                              fontSize: "1rem",
                              fontWeight: 500,
                              color: "#140342",
                            }}
                          >
                            {score.taskType || "Other"}
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              fontSize: "1rem",
                              fontWeight: 500,
                              color: "#140342",
                            }}
                          >
                            {score.pteScores.pronunciation}
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              fontSize: "1rem",
                              fontWeight: 500,
                              color: "#140342",
                            }}
                          >
                            {score.pteScores.fluency}
                          </TableCell>
                          <TableCell
                            align="right"
                            sx={{
                              fontSize: "1rem",
                              fontWeight: 500,
                              color: "#140342",
                            }}
                          >
                            {score.pteScores.content || 0}
                          </TableCell>
                          <TableCell align="right">
                            <Button
                              variant="contained"
                              size="medium"
                              onClick={() => handleQuestionClick(score)}
                              sx={{
                                backgroundColor: "#140342",
                                "&:hover": {
                                  backgroundColor: "#281C63",
                                },
                                fontWeight: 600,
                                borderRadius: "8px",
                              }}
                            >
                              Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </StyledBox>
            </>
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No speaking results found for this attempt.
              </Typography>
            </Box>
          )}

          <Dialog
            open={showDetailedScores}
            onClose={() => setShowDetailedScores(false)}
            maxWidth="md"
            fullWidth
            PaperProps={{
              sx: {
                borderRadius: 2,
              },
            }}
          >
            <DialogTitle>
              <StyledTypography variant="h5">
                Detailed Scores - {selectedQuestion?.taskType || "Question"}
              </StyledTypography>
            </DialogTitle>
            <DialogContent>
              {selectedQuestion && (
                <Box p={3}>
                  <Grid container spacing={3}>
                    <Grid item xs={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Pronunciation</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedQuestion.pteScores.pronunciation}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Fluency</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedQuestion.pteScores.fluency}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Content</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedQuestion.pteScores.content || 0}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12}>
                      <ScoreDisplay>
                        <StyledSubTypography>Overall</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedQuestion?.pteScores
                            ? calculateOverallScore(selectedQuestion.pteScores)
                            : 0}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                  </Grid>
                  {selectedQuestion.media?.url && (
                    <Box mt={4}>
                      <StyledTypography variant="h6" gutterBottom>
                        Recording
                      </StyledTypography>
                      <audio
                        controls
                        style={{
                          width: "100%",
                          borderRadius: "8px",
                        }}
                      >
                        <source
                          src={selectedQuestion.media.url}
                          type="audio/wav"
                        />
                        Your browser does not support the audio element.
                      </audio>
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
          </Dialog>
        </TabPanel>

        <TabPanel value={value} index={2}>
          {writingScores.length > 0 || essayScores.length > 0 ? (
            <>
              {/* Combined Writing Performance Overview */}
              <StyledCard>
                <CardContent>
                  <StyledTypography variant="h5" gutterBottom>
                    Overall Writing Performance
                  </StyledTypography>
                  <Grid container spacing={2}>
                    {writingScores.length > 0 && (
                      <Grid item xs={12} md={6}>
                        <Paper
                          sx={{
                            p: 2,
                            backgroundColor: "#f8f9fa",
                            borderRadius: 2,
                          }}
                        >
                          <Typography
                            variant="h6"
                            sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                          >
                            Summarize Written Text
                          </Typography>
                          {writingAverageScores && (
                            <Grid container spacing={2}>
                              <Grid item xs={6}>
                                <ScoreDisplay
                                  sx={{ minHeight: "auto", p: 1.5 }}
                                >
                                  <Typography
                                    variant="h5"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {writingAverageScores.content}
                                  </Typography>
                                  <Typography variant="body2">
                                    Content
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={6}>
                                <ScoreDisplay
                                  sx={{ minHeight: "auto", p: 1.5 }}
                                >
                                  <Typography
                                    variant="h5"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {writingAverageScores.form}
                                  </Typography>
                                  <Typography variant="body2">Form</Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={6}>
                                <ScoreDisplay
                                  sx={{ minHeight: "auto", p: 1.5 }}
                                >
                                  <Typography
                                    variant="h5"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {writingAverageScores.grammar}
                                  </Typography>
                                  <Typography variant="body2">
                                    Grammar
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={6}>
                                <ScoreDisplay
                                  sx={{ minHeight: "auto", p: 1.5 }}
                                >
                                  <Typography
                                    variant="h5"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {writingAverageScores.vocabulary}
                                  </Typography>
                                  <Typography variant="body2">
                                    Vocabulary
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                            </Grid>
                          )}
                        </Paper>
                      </Grid>
                    )}

                    {essayScores.length > 0 && (
                      <Grid item xs={12} md={6}>
                        <Paper
                          sx={{
                            p: 2,
                            backgroundColor: "#f0f7ff",
                            borderRadius: 2,
                          }}
                        >
                          <Typography
                            variant="h6"
                            sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                          >
                            Essay Writing
                          </Typography>
                          {essayAverageScores && (
                            <Grid container spacing={1}>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {essayAverageScores.content}
                                  </Typography>
                                  <Typography variant="caption">
                                    Content
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {essayAverageScores.form}
                                  </Typography>
                                  <Typography variant="caption">
                                    Form
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {essayAverageScores.grammar}
                                  </Typography>
                                  <Typography variant="caption">
                                    Grammar
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {essayAverageScores.spelling}
                                  </Typography>
                                  <Typography variant="caption">
                                    Spelling
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {essayAverageScores.vocabularyRange}
                                  </Typography>
                                  <Typography variant="caption">
                                    Vocab Range
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={4}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {essayAverageScores.generalLinguisticRange}
                                  </Typography>
                                  <Typography variant="caption">
                                    Linguistic
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                              <Grid item xs={12}>
                                <ScoreDisplay sx={{ minHeight: "auto", p: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{ fontWeight: 700, color: "#140342" }}
                                  >
                                    {
                                      essayAverageScores.developmentStructureCoherence
                                    }
                                  </Typography>
                                  <Typography variant="caption">
                                    Development & Structure
                                  </Typography>
                                </ScoreDisplay>
                              </Grid>
                            </Grid>
                          )}
                        </Paper>
                      </Grid>
                    )}
                  </Grid>
                </CardContent>
              </StyledCard>

              {/* Writing Performance Chart */}
              <StyledCard>
                <CardContent>
                  <StyledTypography variant="h5" gutterBottom>
                    Writing Performance Chart
                  </StyledTypography>
                  <Box height={400}>
                    <ResponsiveContainer>
                      <BarChart data={combinedWritingChartData}>
                        <CartesianGrid
                          strokeDasharray="3 3"
                          strokeOpacity={0.1}
                        />
                        <XAxis
                          dataKey="name"
                          tick={{
                            fontSize: "1rem",
                            fontWeight: 500,
                            fill: "#140342",
                          }}
                        />
                        <YAxis
                          tick={{
                            fontSize: "1rem",
                            fontWeight: 500,
                            fill: "#140342",
                          }}
                        />
                        <Tooltip
                          contentStyle={{
                            borderRadius: "8px",
                            fontWeight: 500,
                            fontSize: "1rem",
                            color: "#140342",
                          }}
                        />
                        <Legend
                          wrapperStyle={{
                            fontWeight: 500,
                            fontSize: "1.1rem",
                            color: "#140342",
                          }}
                        />
                        <Bar
                          dataKey="content"
                          fill="#140342"
                          name="Content"
                          radius={[4, 4, 0, 0]}
                        />
                        <Bar
                          dataKey="form"
                          fill="#281C63"
                          name="Form"
                          radius={[4, 4, 0, 0]}
                        />
                        <Bar
                          dataKey="grammar"
                          fill="#3D3584"
                          name="Grammar"
                          radius={[4, 4, 0, 0]}
                        />
                        {essayScores.length > 0 && (
                          <>
                            <Bar
                              dataKey="spelling"
                              fill="#4E4BA5"
                              name="Spelling"
                              radius={[4, 4, 0, 0]}
                            />
                            <Bar
                              dataKey="vocabularyRange"
                              fill="#5B5BC6"
                              name="Vocab Range"
                              radius={[4, 4, 0, 0]}
                            />
                            <Bar
                              dataKey="generalLinguisticRange"
                              fill="#6B6BE7"
                              name="Linguistic"
                              radius={[4, 4, 0, 0]}
                            />
                            <Bar
                              dataKey="developmentStructureCoherence"
                              fill="#7B7BF8"
                              name="Development"
                              radius={[4, 4, 0, 0]}
                            />
                          </>
                        )}
                        {writingScores.length > 0 && (
                          <Bar
                            dataKey="vocabulary"
                            fill="#4E4BA5"
                            name="Vocabulary"
                            radius={[4, 4, 0, 0]}
                          />
                        )}
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </StyledCard>

              {/* Writing Question Details */}
              <StyledBox>
                <StyledTypography variant="h5" gutterBottom>
                  Writing Question Details
                </StyledTypography>

                {/* Summarize Written Text Table */}
                {writingScores.length > 0 && (
                  <>
                    <Typography
                      variant="h6"
                      sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                    >
                      Summarize Written Text
                    </Typography>
                    <TableContainer
                      component={Paper}
                      sx={{ borderRadius: 2, boxShadow: 2, mb: 4 }}
                    >
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Task Type
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Content
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Form
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Grammar
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Vocabulary
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "1.1rem",
                                color: "#140342",
                              }}
                            >
                              Actions
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {writingScores.map((score, index) => (
                            <TableRow key={score.answerId || index}>
                              <TableCell
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                Summarize Written Text
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(score.pteScores?.content)}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(score.pteScores?.form)}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(score.pteScores?.grammar)}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(score.pteScores?.vocabulary)}
                              </TableCell>
                              <TableCell align="right">
                                <Button
                                  variant="contained"
                                  size="medium"
                                  onClick={() =>
                                    handleWritingQuestionClick(score)
                                  }
                                  sx={{
                                    backgroundColor: "#140342",
                                    "&:hover": { backgroundColor: "#281C63" },
                                    fontWeight: 600,
                                    borderRadius: "8px",
                                  }}
                                >
                                  Details
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                )}

                {/* Essay Writing Table */}
                {essayScores.length > 0 && (
                  <>
                    <Typography
                      variant="h6"
                      sx={{ fontWeight: 600, color: "#140342", mb: 2 }}
                    >
                      Essay Writing
                    </Typography>
                    <TableContainer
                      component={Paper}
                      sx={{ borderRadius: 2, boxShadow: 2 }}
                    >
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell
                              sx={{
                                fontWeight: 600,
                                fontSize: "1rem",
                                color: "#140342",
                              }}
                            >
                              Task Type
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "0.9rem",
                                color: "#140342",
                              }}
                            >
                              Content
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "0.9rem",
                                color: "#140342",
                              }}
                            >
                              Form
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "0.9rem",
                                color: "#140342",
                              }}
                            >
                              Grammar
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "0.9rem",
                                color: "#140342",
                              }}
                            >
                              Spelling
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "0.9rem",
                                color: "#140342",
                              }}
                            >
                              Vocab Range
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "0.9rem",
                                color: "#140342",
                              }}
                            >
                              Linguistic
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "0.9rem",
                                color: "#140342",
                              }}
                            >
                              Development
                            </TableCell>
                            <TableCell
                              align="right"
                              sx={{
                                fontWeight: 600,
                                fontSize: "0.9rem",
                                color: "#140342",
                              }}
                            >
                              Actions
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {essayScores.map((score, index) => (
                            <TableRow key={score.answerId || index}>
                              <TableCell
                                sx={{
                                  fontSize: "1rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                Essay Writing
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "0.9rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(score.pteScores?.content)}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "0.9rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(score.pteScores?.form)}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "0.9rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(score.pteScores?.grammar)}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "0.9rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(score.pteScores?.spelling)}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "0.9rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(
                                  score.pteScores?.vocabularyRange
                                )}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "0.9rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(
                                  score.pteScores?.generalLinguisticRange
                                )}
                              </TableCell>
                              <TableCell
                                align="right"
                                sx={{
                                  fontSize: "0.9rem",
                                  fontWeight: 500,
                                  color: "#140342",
                                }}
                              >
                                {getScoreValue(
                                  score.pteScores?.developmentStructureCoherence
                                )}
                              </TableCell>
                              <TableCell align="right">
                                <Button
                                  variant="contained"
                                  size="medium"
                                  onClick={() =>
                                    handleEssayQuestionClick(score)
                                  }
                                  sx={{
                                    backgroundColor: "#140342",
                                    "&:hover": { backgroundColor: "#281C63" },
                                    fontWeight: 600,
                                    borderRadius: "8px",
                                  }}
                                >
                                  Details
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                )}
              </StyledBox>
            </>
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No writing results found for this attempt.
              </Typography>
            </Box>
          )}

          {/* Summarize Written Text Dialog */}
          <Dialog
            open={showWritingDetailedScores}
            onClose={() => setShowWritingDetailedScores(false)}
            maxWidth="lg"
            fullWidth
            PaperProps={{ sx: { borderRadius: 2 } }}
          >
            <DialogTitle>
              <StyledTypography variant="h5">
                Detailed Scores - Summarize Written Text
              </StyledTypography>
            </DialogTitle>
            <DialogContent>
              {selectedWritingQuestion && (
                <Box p={3}>
                  <Grid container spacing={3}>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Content</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedWritingQuestion.pteScores?.content
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.content
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Form</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedWritingQuestion.pteScores?.form
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.form?.maxScore ||
                            2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Grammar</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedWritingQuestion.pteScores?.grammar
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.grammar
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Vocabulary</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedWritingQuestion.pteScores?.vocabulary
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.vocabulary
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={2.4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Total</StyledSubTypography>
                        <Typography
                          variant="h3"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedWritingQuestion.pteScores?.totalScore || 0}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedWritingQuestion.pteScores?.maxScore || 8}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                  </Grid>

                  {/* Display suggestions for each category */}
                  <Box sx={{ mt: 4 }}>
                    <StyledTypography variant="h6" gutterBottom>
                      Detailed Feedback
                    </StyledTypography>
                    <Grid container spacing={2}>
                      {["content", "form", "grammar", "vocabulary"].map(
                        (category) => (
                          <Grid item xs={12} sm={6} key={category}>
                            <Paper
                              sx={{
                                p: 2,
                                backgroundColor: "#f8f9fa",
                                borderRadius: 2,
                              }}
                            >
                              <Typography
                                variant="subtitle2"
                                sx={{
                                  fontWeight: 600,
                                  color: "#140342",
                                  mb: 1,
                                }}
                              >
                                {category.charAt(0).toUpperCase() +
                                  category.slice(1)}{" "}
                                Feedback:
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "#666" }}
                              >
                                {selectedWritingQuestion.pteScores?.[category]
                                  ?.suggestion ||
                                  "No specific feedback available for this category."}
                              </Typography>
                            </Paper>
                          </Grid>
                        )
                      )}
                    </Grid>
                  </Box>

                  {/* Display student answer and word count */}
                  <Box sx={{ mt: 4 }}>
                    <StyledTypography variant="h6" gutterBottom>
                      Your Summary
                    </StyledTypography>
                    <Paper
                      sx={{ p: 2, backgroundColor: "#f8f9fa", borderRadius: 2 }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          mb: 2,
                        }}
                      >
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Word Count:{" "}
                          {selectedWritingQuestion.pteScores?.wordCount || 0}
                        </Typography>
                        <Typography variant="body2" sx={{ color: "#666" }}>
                          Required: 5-75 words
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{ fontStyle: "italic", lineHeight: 1.6 }}
                      >
                        "
                        {selectedWritingQuestion.answer || "No answer provided"}
                        "
                      </Typography>
                    </Paper>
                  </Box>

                  {/* Display annotated text if available */}
                  {selectedWritingQuestion.pteScores?.annotatedText && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Annotated Analysis
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: "#f8f9fa",
                          borderRadius: 2,
                        }}
                      >
                        <div
                          dangerouslySetInnerHTML={{
                            __html:
                              selectedWritingQuestion.pteScores.annotatedText,
                          }}
                          style={{ lineHeight: 1.6, fontSize: "1rem" }}
                        />
                      </Paper>
                    </Box>
                  )}

                  {/* Display original text for reference */}
                  {selectedWritingQuestion.prompt && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Original Text (Reference)
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: "#e3f2fd",
                          borderRadius: 2,
                        }}
                      >
                        <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                          {selectedWritingQuestion.prompt}
                        </Typography>
                      </Paper>
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
          </Dialog>

          {/* Essay Writing Dialog */}
          <Dialog
            open={showEssayDetailedScores}
            onClose={() => setShowEssayDetailedScores(false)}
            maxWidth="lg"
            fullWidth
            PaperProps={{ sx: { borderRadius: 2 } }}
          >
            <DialogTitle>
              <StyledTypography variant="h5">
                Detailed Scores - Essay Writing
              </StyledTypography>
            </DialogTitle>
            <DialogContent>
              {selectedEssayQuestion && (
                <Box p={3}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <ScoreDisplay>
                        <StyledSubTypography>Content</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores?.content
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.content?.maxScore ||
                            3}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <ScoreDisplay>
                        <StyledSubTypography>Form</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(selectedEssayQuestion.pteScores?.form)}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.form?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <ScoreDisplay>
                        <StyledSubTypography>Grammar</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores?.grammar
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.grammar?.maxScore ||
                            2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <ScoreDisplay>
                        <StyledSubTypography>Spelling</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores?.spelling
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.spelling
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>
                          Vocabulary Range
                        </StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores?.vocabularyRange
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores?.vocabularyRange
                            ?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>
                          Linguistic Range
                        </StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores
                              ?.generalLinguisticRange
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores
                            ?.generalLinguisticRange?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <ScoreDisplay>
                        <StyledSubTypography>Development</StyledSubTypography>
                        <Typography
                          variant="h4"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {getScoreValue(
                            selectedEssayQuestion.pteScores
                              ?.developmentStructureCoherence
                          )}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max:{" "}
                          {selectedEssayQuestion.pteScores
                            ?.developmentStructureCoherence?.maxScore || 2}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                    <Grid item xs={12}>
                      <ScoreDisplay>
                        <StyledSubTypography>Total Score</StyledSubTypography>
                        <Typography
                          variant="h2"
                          sx={{ fontWeight: 700, color: "#140342" }}
                        >
                          {selectedEssayQuestion.pteScores?.totalScore || 0}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: "#666", mt: 1 }}
                        >
                          Max: {selectedEssayQuestion.pteScores?.maxScore || 15}
                        </Typography>
                      </ScoreDisplay>
                    </Grid>
                  </Grid>

                  {/* Display suggestions for each category */}
                  <Box sx={{ mt: 4 }}>
                    <StyledTypography variant="h6" gutterBottom>
                      Detailed Feedback
                    </StyledTypography>
                    <Grid container spacing={2}>
                      {[
                        "content",
                        "form",
                        "grammar",
                        "spelling",
                        "vocabularyRange",
                        "generalLinguisticRange",
                        "developmentStructureCoherence",
                      ].map((category) => (
                        <Grid item xs={12} sm={6} md={4} key={category}>
                          <Paper
                            sx={{
                              p: 2,
                              backgroundColor: "#f8f9fa",
                              borderRadius: 2,
                            }}
                          >
                            <Typography
                              variant="subtitle2"
                              sx={{ fontWeight: 600, color: "#140342", mb: 1 }}
                            >
                              {category === "vocabularyRange"
                                ? "Vocabulary Range"
                                : category === "generalLinguisticRange"
                                ? "Linguistic Range"
                                : category === "developmentStructureCoherence"
                                ? "Development & Structure"
                                : category.charAt(0).toUpperCase() +
                                  category.slice(1)}{" "}
                              Feedback:
                            </Typography>
                            <Typography variant="body2" sx={{ color: "#666" }}>
                              {selectedEssayQuestion.pteScores?.[category]
                                ?.suggestion ||
                                "No specific feedback available for this category."}
                            </Typography>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>

                  {/* Relevance Check */}
                  {selectedEssayQuestion.pteScores?.relevanceCheck && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Relevance Check
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: selectedEssayQuestion.pteScores
                            .relevanceCheck.isRelevant
                            ? "#e8f5e8"
                            : "#ffebee",
                          borderRadius: 2,
                          border: `1px solid ${
                            selectedEssayQuestion.pteScores.relevanceCheck
                              .isRelevant
                              ? "#4caf50"
                              : "#f44336"
                          }`,
                        }}
                      >
                        <Typography
                          variant="subtitle2"
                          sx={{
                            fontWeight: 600,
                            color: selectedEssayQuestion.pteScores
                              .relevanceCheck.isRelevant
                              ? "#2e7d32"
                              : "#d32f2f",
                            mb: 1,
                          }}
                        >
                          {selectedEssayQuestion.pteScores.relevanceCheck
                            .isRelevant
                            ? "✓ Relevant to Prompt"
                            : "✗ Not Relevant to Prompt"}
                        </Typography>
                        <Typography variant="body2" sx={{ color: "#666" }}>
                          {
                            selectedEssayQuestion.pteScores.relevanceCheck
                              .explanation
                          }
                        </Typography>
                      </Paper>
                    </Box>
                  )}

                  {/* Display student answer and word count */}
                  <Box sx={{ mt: 4 }}>
                    <StyledTypography variant="h6" gutterBottom>
                      Your Essay
                    </StyledTypography>
                    <Paper
                      sx={{ p: 2, backgroundColor: "#f8f9fa", borderRadius: 2 }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          mb: 2,
                        }}
                      >
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Word Count:{" "}
                          {selectedEssayQuestion.pteScores?.wordCount || 0}
                        </Typography>
                        <Typography variant="body2" sx={{ color: "#666" }}>
                          Recommended: 200-300 words
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        sx={{ lineHeight: 1.8, whiteSpace: "pre-wrap" }}
                      >
                        {selectedEssayQuestion.answer || "No answer provided"}
                      </Typography>
                    </Paper>
                  </Box>

                  {/* Display annotated text if available */}
                  {selectedEssayQuestion.pteScores?.annotatedText && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Annotated Analysis
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: "#f8f9fa",
                          borderRadius: 2,
                        }}
                      >
                        <div
                          dangerouslySetInnerHTML={{
                            __html:
                              selectedEssayQuestion.pteScores.annotatedText,
                          }}
                          style={{ lineHeight: 1.8, fontSize: "1rem" }}
                        />
                      </Paper>
                    </Box>
                  )}

                  {/* Display essay prompt for reference */}
                  {selectedEssayQuestion.prompt && (
                    <Box sx={{ mt: 4 }}>
                      <StyledTypography variant="h6" gutterBottom>
                        Essay Prompt (Reference)
                      </StyledTypography>
                      <Paper
                        sx={{
                          p: 2,
                          backgroundColor: "#e3f2fd",
                          borderRadius: 2,
                        }}
                      >
                        <Typography variant="body2" sx={{ lineHeight: 1.6 }}>
                          {selectedEssayQuestion.prompt}
                        </Typography>
                      </Paper>
                    </Box>
                  )}
                </Box>
              )}
            </DialogContent>
          </Dialog>
        </TabPanel>
        <TabPanel value={value} index={3}>
          {allMockTestQuestions.reading.length > 0 ? (
            (() => {
              const completeData = createCompleteSectionData(
                "reading",
                readingScores
              );
              console.log("Reading complete data:", completeData);
              return (
                <ResultSection
                  data={completeData}
                  section="reading"
                  onResultsCalculated={handleReadingResults}
                />
              );
            })()
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No reading questions in this mock test.
              </Typography>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={value} index={4}>
          {allMockTestQuestions.listening.length > 0 ? (
            (() => {
              const completeData = createCompleteSectionData(
                "listening",
                listeningScores
              );
              console.log("Listening complete data:", completeData);
              return (
                <ResultSection
                  data={completeData}
                  section="listening"
                  onResultsCalculated={handleListeningResults}
                />
              );
            })()
          ) : (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="h6" color="text.secondary">
                No listening questions in this mock test.
              </Typography>
            </Box>
          )}
        </TabPanel>
        {/* The rest of the tabs (Speaking, Writing, Reading, Listening) would continue here with their full implementations */}
      </Paper>
    </Container>
  );
};

ResultComponent.propTypes = {
  mockTestId: PropTypes.string.isRequired,
  userId: PropTypes.string.isRequired,
  attemptNumber: PropTypes.number,
};

export default ResultComponent;
